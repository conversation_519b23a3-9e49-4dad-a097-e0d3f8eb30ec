<template>
  <view class="container">
    <view class="container-top">
      患者您好，请填写您的基本信息，就诊时医生将为您提供更好的诊疗服务
    </view>
    <view class="container-user" v-if="showUser">
      <view class="container-user-line">
        <text>
          姓名
          <text style="color: red; margin-left: 12rpx">*</text>
        </text>
        <input
          class="container-user-line-right container-user-line-input"
          placeholder="请输入你的姓名"
          v-model="userParams.Name"
        />
      </view>
      <u-divider />
      <view class="container-user-line">
        <text>
          年龄
          <text style="color: red; margin-left: 12rpx">*</text>
        </text>
        <input
          class="container-user-line-right container-user-line-input"
          placeholder="请输入你的年龄"
          v-model="userParams.Age"
        />
      </view>
      <u-divider />
      <view class="container-user-line">
        <text>
          性别
          <text style="color: red; margin-left: 12rpx">*</text>
        </text>
        <view class="container-user-line-right container-user-line-sex">
          <block v-for="(item, index) in ['男', '女']" :key="index">
            <view
              class="container-user-line-sex-item"
              :class="
                userParams.Sex === item ? 'container-user-line-sex-select' : ''
              "
              @click="userParams.Sex = item"
            >
              {{ item }}
            </view>
          </block>
        </view>
      </view>
      <view class="btnButtomStyle" @click="handleNextClick"> 下一步 </view>
    </view>
    <block v-else>
      <view class="container-chat">
        <u-list
          :height="scrollHeight"
          :showScrollbar="false"
          :scrollTop="listScrollTop"
          :scrollWithAnimation="true"
          :customStyle="{
            padding: '0 32rpx',
            backgroundColor: '#F7F7F7',
            flex: 1,
          }"
          :enableFlex="true"
        >
          <!-- 消息体 -->
          <u-list-item v-for="(message, index) in messageList" :key="index">
            <!-- 患者消息 -->
            <view
              class="session-custom-cell message-animation-right"
              v-if="message.role === 'patient'"
              :class="{ 'message-enter': message.isNew }"
            >
              <view class="time-view" v-if="message.showTime">
                <text>{{ message.sendTime }}</text>
              </view>
              <view class="session-body-view session-body-view--right">
                <MpAvatar
                  :src="patientAvatar"
                  width="40px"
                  height="40px"
                ></MpAvatar>
                <view class="session-content-view session-content-view--right">
                  <view
                    class="session-content-body-view session-content-body-view--right"
                  >
                    <view class="session-custom-text-sender-cell">
                      <text class="custom-text">{{ message.text }}</text>
                    </view>
                    <!-- 错误提示图标 - 显示在消息气泡左侧 -->
                    <view
                      v-if="message.hasError"
                      class="error-retry-icon"
                      @click="handleRetryMessage(message, index)"
                    >
                      <image
                        class="error-icon"
                        src="/subPackIndex/static/icon-error.png"
                      />
                    </view>
                  </view>
                  <!-- 重新描述按钮 - 仅对最新患者消息显示，放在文字下面 -->
                  <view
                    v-if="index === latestPatientMessageIndex && index !== 0"
                    class="redescribe-button"
                    @click="handleRedescribe(message, index)"
                  >
                    <image
                      class="redescribe-icon"
                      src="/subPackIndex/static/icon-back.png"
                    />
                    <text class="redescribe-text">重新描述</text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 医生消息 -->
            <view
              class="session-custom-cell message-animation-left"
              v-else-if="message.role === 'doctor' && !message.hidden"
              :class="{ 'message-enter': message.isNew }"
            >
              <view class="time-view" v-if="message.showTime">
                <text>{{ message.sendTime }}</text>
              </view>
              <view class="session-body-view">
                <MpAvatar
                  :src="doctorAvatar"
                  width="40px"
                  height="40px"
                ></MpAvatar>
                <view class="session-content-view">
                  <view class="session-content-body-view">
                    <view
                      class="session-custom-text-receiver-cell"
                      :class="{ 'waiting-message': message.type === 'waiting' }"
                    >
                      <text
                        class="custom-text"
                        v-if="message.type === 'text'"
                        >{{ message.text }}</text
                      >
                      <!-- 等待提示消息 -->
                      <view
                        v-if="message.type === 'waiting'"
                        class="waiting-content"
                      >
                        <view class="waiting-dots">
                          <view class="dot" v-for="n in 3" :key="n"></view>
                        </view>
                        <text class="waiting-text">{{ message.text }}</text>
                      </view>
                      <!-- 语音播放按钮 - 仅对医生的文本消息显示 -->
                      <view
                        v-if="message.type === 'text'"
                        class="voice-play-button"
                        :class="{
                          'voice-play-button--playing':
                            currentPlayingIndex === index,
                        }"
                        @click="handleVoicePlay(message, index)"
                      >
                        <image
                          class="voice-play-icon"
                          :src="
                            currentPlayingIndex === index
                              ? '/subPackIndex/static/voice.gif'
                              : '/subPackIndex/static/voice.png'
                          "
                        />
                        <text class="voice-play-text">
                          {{
                            currentPlayingIndex === index
                              ? '停止播放'
                              : '语音播放'
                          }}
                        </text>
                      </view>
                      <view
                        class="options-container"
                        v-if="message.type === 'objective-options'"
                      >
                        <view
                          class="option-item"
                          v-for="(option, optionIndex) in message.options"
                          :key="optionIndex"
                          @click="handleOptionClick(option, message, index)"
                          :class="{ 'option-item--selected': option.selected }"
                        >
                          <text class="option-text">{{ option.text }}</text>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </u-list-item>
        </u-list>
      </view>
      <!-- 键盘输入区域 -->
      <view
        class="container-keyboard"
        :style="{
          transform: isKeyboardShow
            ? `translateY(-${actualKeyboardHeight}px)`
            : 'translateY(0)',
          transition: 'transform 0.25s ease-out',
        }"
      >
        <!-- 语音输入模式 -->
        <view class="voice-mode" v-if="isVoiceMode">
          <view class="voice-toggle-left" @click="toggleInputMode">
            <image
              class="voice-icon"
              src="/subPackIndex/static/icon-keyboard.png"
            />
          </view>
          <view
            class="voice-button"
            :class="{ 'voice-button--recording': isRecording }"
            @touchstart="startVoiceRecord"
            @touchmove="onVoiceTouchMove"
            @touchend="endVoiceRecord"
            @touchcancel="cancelVoiceRecord"
          >
            <text class="voice-text">按住 说话</text>
          </view>
        </view>

        <!-- 文本输入模式 -->
        <view class="text-mode" v-else>
          <view class="text-toggle-left" @click="toggleInputMode">
            <image
              class="voice-icon"
              src="/subPackIndex/static/icon-voice.png"
            />
          </view>
          <view class="input-wrapper">
            <textarea
              v-model="inputText"
              class="text-input"
              placeholder="请输入..."
              :auto-height="true"
              :show-confirm-bar="false"
              :adjust-position="false"
              :focus="textareaFocus"
              @focus="onInputFocus"
              @blur="onInputBlur"
              @input="onTextInput"
              @keyboardheightchange="onKeyboardHeightChange"
            />
          </view>
          <view
            class="confirm-button"
            :class="{ 'confirm-button--active': inputText.trim() }"
            @click="sendTextMessage"
          >
            <text class="confirm-text">确认</text>
          </view>
        </view>
      </view>
    </block>

    <!-- 录音弹窗 -->
    <view
      class="record-modal"
      v-if="showRecordModal"
      @click="cancelVoiceRecord"
    >
      <view class="record-content" @click.stop>
        <view class="record-animation">
          <view
            class="record-circle"
            :class="{ 'record-circle--cancel': isInCancelArea }"
          >
            <view class="record-inner-circle">
              <image
                class="record-mic-icon"
                src="/static/icons/mic-white.png"
              />
            </view>
          </view>
          <view class="record-waves" v-if="!isInCancelArea">
            <view class="wave" v-for="n in 3" :key="n"></view>
          </view>
        </view>
        <!-- <view class="record-text">{{
          isInCancelArea ? '松手取消发送' : recordText
        }}</view> -->
        <view class="record-tip">{{
          isInCancelArea ? '手指上滑，取消发送' : '松手发送；手指上滑，取消发送'
        }}</view>

        <!-- 取消区域 -->
        <view
          class="cancel-area"
          :class="{ 'cancel-area--active': isInCancelArea }"
        >
          <view class="cancel-icon">✕</view>
          <view class="cancel-text">取消发送</view>
        </view>
      </view>
    </view>
    <!-- 身体部位选择遮罩层 -->
    <view class="container-bodyDialog" v-if="openBodyPartDialog">
      <!-- 遮罩背景 -->
      <view class="mask-overlay" @click="closeMask">
        <!-- 遮罩内容区域 -->
        <view class="mask-content" @click.stop>
          <view class="mask-header">
            <text class="mask-title"
              >这个不舒服具体在身体哪个地方呢?请在下方图片中点击您最不舒服的部位。(多选）</text
            >
          </view>
          <view class="mask-body" @click.stop>
            <image
              class="mask-body-img"
              src="/subPackIndex/static/icon-body.png"
            ></image>
            <block v-for="(item, index) in bodyPoints" :key="index">
              <image
                @click.stop="handlePointClick(item.Ext, $event)"
                :style="{ top: item.Top, left: item.Left, right: item.Right }"
                :src="
                  item.IsSelect
                    ? '/subPackIndex/static/icon-select.png'
                    : '/subPackIndex/static/icon-default.png'
                "
                class="mask-body-point"
              ></image>
            </block>
            <!-- 这里可以放置身体图片或其他内容 -->
          </view>
          <view class="mask-footer">
            <view class="confirm-btn" @click.stop="confirmSelection">
              <text class="btn-text">确认</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <u-overlay :show="showSpinalOverlay" @click="showSpinalOverlay = false">
      <view class="spinal-overlay-warp">
        <image
          src="/subPackIndex/static/icon-finish.png"
          style="width: 580rpx; height: 140rpx"
        />
        <view class="spinal-overlay-warp-container">
          <text class="spinal-overlay-warp-container-texttop"
            >感谢您的认真填写，信息已全部整理完转达给医生，请耐心等待。</text
          >
          <block v-if="assistantWeChatQrCode">
            <view class="spinal-overlay-warp-container-banner">
              <!-- <ikun-qrcode
                v-if="showSpinalOverlay"
                width="260"
                height="260"
                unit="rpx"
                :data="assistantWeChatQrCode"
              /> -->
              <image
                v-if="showSpinalOverlay"
                :show-menu-by-longpress="true"
                :src="assistantWeChatQrCode"
                class="spinal-overlay-warp-container-banner-image"
              ></image>
            </view>
            <view class="spinal-overlay-warp-container-text"
              >长按可添加医生助手微信，离院后有任何 问题随时联系</view
            >
          </block>
        </view>
      </view>
    </u-overlay>
  </view>
</template>
<script>
const app = getApp();
import request from '@/libs/request';
import { guid } from '@/utils/validate';
import bodyPoints from './data/bodySelect.js';
import {
  getDocInfoMation,
  setPreVisitRecord,
  updatePreVisitRecordState,
  insertPreApplyRecord,
} from '@/api/consult.js';
import dayjs from 'dayjs';
import MpAvatar from '@/components/mp-avatar/mp-avatar.vue';
import { preconsult } from '@/api/aipowered.js';
import MPTag from '@/components/mp-tag/u-tag.vue';
import { updatePartDetail } from '@/services/userAuthen';

// 引入微信同声传译插件
const plugin = requirePlugin('WechatSI');
const manager = plugin.getRecordRecognitionManager();
const WechatSI = plugin;

export default {
  components: {
    MpAvatar,
    MPTag,
  },
  data() {
    return {
      // 预问诊医生Id
      doctorId: '',
      // 消息列表， 其中role === 'patient' 为患者消息（在页面的右侧） role === 'doctor' 为医生发送的消息（在页面的左侧）
      // type 表示消息类型（text、objective-options等）
      messageList: [],
      // 滚动区域高度
      scrollHeight: 0,
      // 列表滚动位置
      listScrollTop: 999999,
      // 患者头像
      patientAvatar: '',
      // 患者姓名
      patientName: '',
      // 医生头像
      doctorAvatar:
        'https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/defluat-doctor.png',
      // 医生姓名
      doctorName: '',
      // 键盘相关
      keyboardHeight: 120, // 键盘区域高度
      actualKeyboardHeight: 0, // 实际键盘弹出高度
      isKeyboardShow: false, // 键盘是否弹出
      inputText: '', // 输入文本
      isVoiceMode: true, // 是否为语音模式，默认为语音模式
      isRecording: false, // 是否正在录音
      // 录音弹窗相关
      showRecordModal: false, // 是否显示录音弹窗
      recordText: '正在聆听...', // 录音提示文字
      recognizedText: '', // 识别到的文字
      isInCancelArea: false, // 是否在取消区域
      isCancelled: false, // 是否取消录音
      // 长按相关
      longPressTimer: null, // 长按计时器
      isLongPressTriggered: false, // 是否已触发长按
      // 触摸相关
      touchStartY: 0, // 触摸开始的Y坐标
      touchCurrentY: 0, // 当前触摸的Y坐标
      // 语音播放相关
      innerAudioContext: null, // 音频播放器
      currentPlayingIndex: -1, // 当前播放的消息索引
      dialogueEnded: false, // 整个流程是否结束
      textareaFocus: false, // 输入框是否聚焦
      openBodyPartDialog: false, // 是否打开弹窗 选择部位 只有当questionId === 3 的时候 才会有机会打开它
      bodyPoints: bodyPoints, // 选择部位
      // 等待提示相关
      waitingMessageIndex: -1, // 等待提示消息的索引
      // 录音状态管理
      recordingState: 'IDLE', // 录音状态：IDLE-空闲, STARTING-启动中, RECORDING-录音中, STOPPING-停止中
      recordRetryCount: 0, // 录音重试次数
      maxRecordRetries: 3, // 最大重试次数
      sessionContext: {}, // 保存每次返回的Context
      preVisitRecordId: '', // 预问诊id
      sessionId: '', // 当前患者发送消息的guid
      bodyPngSrc: '', // 人体图的src https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/icon-body.png
      showUser: true, // 是否显示填写患者信息
      userParams: {
        Name: app.globalData.userInfo.Name || '',
        Age: app.globalData.userInfo.Age || '',
        Sex: app.globalData.userInfo.Sex || '',
      },
      showSpinalOverlay: false, // 是否展示结束的弹框
      assistantWeChatQrCode: '', // 医助的微信
      isSelectObjective: false, // 是否选择了这次来干嘛
    };
  },
  computed: {
    // 获取最新患者消息的索引
    latestPatientMessageIndex() {
      for (let i = this.messageList.length - 1; i >= 0; i--) {
        if (this.messageList[i].role === 'patient') {
          return i;
        }
      }
      return -1;
    },
  },
  watch: {
    // 监听消息列表变化，自动滚动到底部
    messageList: {
      handler() {
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      },
      deep: true,
    },
    dialogueEnded: {
      handler(newValue, oldValue) {
        if (newValue) {
          this.showSpinalOverlay = true;
        }
      },
    },
    showRecordModal: {
      handler(newValue, oldValue) {
        if (!newValue) {
          // 如果录音结束 关闭这个弹框 如果还有在进行中的录音需要马上结束掉
          if (
            this.isRecording &&
            (this.recordingState === 'RECORDING' ||
              this.recordingState === 'STARTING')
          ) {
            console.log('showRecordModal watcher: 弹框关闭，强制停止录音');
            this.isCancelled = true; // 设为取消状态，防止onStop发送消息
            try {
              manager.stop();
            } catch (e) {
              console.error('manager.stop() in watcher failed', e);
              // 即使stop失败，也需要重置状态以防万一
              this.resetRecordingState();
            }
          }
        }
      },
    },
  },
  async onLoad(option) {
    await getApp().isLaunchFinish();
    this.doctorId = option.doctorId;
    // 获取医生基本信息
    await this.onGetDoctorInfo(this.doctorId);
    await this.handleSendBaseInfoToServe();
    this.initPageData();
    // 为messageList中填充消息
    if (app.globalData.userInfo.Name) {
      this.showUser = false;
      this.onSetMessageList();
    }
  },
  async onReady() {
    this.calculateScrollHeight();
    this.initRecord();
    this.initAudioPlayer();
    // 页面准备完成后滚动到底部
    this.$nextTick(() => {
      this.scrollToBottom();
    });
    // 检查录音权限
    this.checkRecordAuth();
  },
  onUnload() {
    // 页面卸载时销毁音频播放器
    if (this.innerAudioContext) {
      this.innerAudioContext.destroy();
      this.innerAudioContext = null;
    }

    // 清理长按计时器
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
  },
  methods: {
    handleNextClick() {
      if (
        !this.userParams.Name ||
        !this.userParams.Age ||
        !this.userParams.Sex
      ) {
        uni.showToast({
          title: '请填写完整信息',
          icon: 'none',
        });
        return;
      }
      if (Number(this.userParams.Age) === NaN) {
        uni.showToast({
          title: '请输入正确的年龄',
          icon: 'none',
        });
        return;
      }
      app.globalData.userInfo.Name = this.userParams.Name;
      app.globalData.userInfo.Age = this.userParams.Age;
      app.globalData.userInfo.Sex = this.userParams.Sex;
      this.showUser = false;
      this.onSetMessageList();
      updatePartDetail({
        UserId: app.globalData.userInfo.Id,
        Name: this.userParams.Name,
        Age: this.userParams.Age,
        Sex: this.userParams.Sex,
      });
    },
    onSendMessageLocal(sessionId, questionInfo, answer) {
      const currentTime = dayjs(new Date()).format('YYYY-MM-DD HH:mm');
      // 患者发送消息
      const patientMessage = {
        role: 'patient',
        type: 'text',
        text: answer,
        sendTime: currentTime,
        showTime: false,
        sessionId: sessionId,
        isNew: true,
        hasError: false, // 初始化错误状态
        retryParams: null, // 初始化重试参数
      };
      this.messageList.push(patientMessage);
      this.triggerMessageAnimation(patientMessage);

      this.sessionId = sessionId;
      // 医生回复消息
      if (questionInfo.Messages.length) {
        if (this.sessionId !== questionInfo.Messages[0].SessionId) return;
        questionInfo.Messages.filter((s) => s.Format !== 'Url').forEach(
          (item) => {
            const doctorMessage = {
              role: 'doctor',
              type:
                item.Format === 'Text'
                  ? 'text'
                  : item.Format === 'Url'
                  ? ''
                  : '',
              text: item.Text,
              sendTime: currentTime,
              showTime: false,
              messageType: item.Type,
              sessionId: item.SessionId,
              isNew: true,
            };
            this.messageList.push(doctorMessage);
            this.triggerMessageAnimation(doctorMessage);
          }
        );
        const sessionId = questionInfo.Messages[0].SessionId;
        this.sessionContext[sessionId] = questionInfo.Context;
        this.dialogueEnded = questionInfo.Completed;
        this.scrollToBottom();
      }
    },
    async handleSendMessageToLocalServeTest(
      question,
      answer,
      context = null,
      guid
    ) {
      // 如果流程结束，后续患者发送的消息都不记录到后台
      console.log('this.dialogueEnded', this.dialogueEnded);
      if (this.dialogueEnded) return;
      const currentTime = dayjs(new Date()).format('YYYY-MM-DD HH:mm');

      // 添加等待提示消息
      const waitingMessage = {
        role: 'doctor',
        type: 'waiting',
        text: '',
        sendTime: currentTime,
        showTime: false,
        isWaiting: true,
        isNew: true,
      };

      this.messageList = this.messageList.filter((s) => s.type !== 'waiting');
      // 使用 Promise 和 async/await 实现100ms等待
      (async () => {
        await new Promise((resolve) => {
          const startTime = Date.now();
          const checkTime = () => {
            if (Date.now() - startTime >= 100) {
              resolve();
            } else {
              // 使用微信小程序的下一帧回调
              this.$nextTick(checkTime);
            }
          };
          checkTime();
        });

        this.messageList.push(waitingMessage);
        this.triggerMessageAnimation(waitingMessage);
        this.waitingMessageIndex = this.messageList.length - 1;
        // 滚动到底部显示等待消息
        this.scrollToBottom();
      })();

      try {
        const params = {
          SessionId: guid,
          Question: question,
          Answer: answer,
          Context: context,
          Name: app.globalData.userInfo.Name,
          Age: app.globalData.userInfo.Age,
          Sex: app.globalData.userInfo.Sex,
          DoctorId: this.doctorId,
          UserId: app.globalData.userInfo.Id,
        };
        this.sessionId = guid;
        const res = await preconsult(params);
        // const res = await request.post(
        //   'https://suited-living-quail.ngrok-free.app/api/bailian/preconsult',
        //   params,
        //   {
        //     timeout: 600000,
        //   }
        // );
        if (res.Type === 200 && res.Data) {
          // 代表请求完成 服务器返回了数据
          if (res.Data.Messages.length) {
            if (this.sessionId !== res.Data.Messages[0].SessionId) return;
            // 删除等待提示消息
            if (this.waitingMessageIndex !== -1) {
              this.messageList.splice(this.waitingMessageIndex, 1);
              this.waitingMessageIndex = -1;
            }
            res.Data.Messages.forEach((item) => {
              const doctorMessage = {
                role: 'doctor',
                type:
                  item.Format === 'Text'
                    ? 'text'
                    : item.Format === 'UrlType'
                    ? 'image'
                    : '',
                text: item.Text,
                sendTime: currentTime,
                showTime: false,
                messageType: item.Type,
                sessionId: item.SessionId,
                isNew: true,
                hidden:
                  item.Format === 'UrlType' && item.Text === 'BodySelection',
              };
              this.messageList.push(doctorMessage);
              this.triggerMessageAnimation(doctorMessage);
            });
            // 如果这次医生发送的消息中有 需要弹出选择部位的图片的消息 就直接弹出部位选择图
            const pngUrlList = res.Data.Messages.filter(
              (s) => s.Format === 'UrlType' && s.Text === 'BodySelection'
            );
            if (pngUrlList && pngUrlList.length) {
              this.openBodyPartDialog = true;
            }
            const sessionId = res.Data.Messages[0].SessionId;
            this.sessionContext[sessionId] = res.Data.Context;
            this.dialogueEnded = res.Data.Completed;
            if (
              res.Data.PurposeIdx >= 0 &&
              typeof res.Data.PurposeIdx === 'number'
            ) {
              this.handleGetConsultTypeToService(res.Data.PurposeIdx);
            }
            this.scrollToBottom();
          } else {
            // 删除等待提示消息
            if (this.waitingMessageIndex !== -1) {
              this.messageList.splice(this.waitingMessageIndex, 1);
              this.waitingMessageIndex = -1;
            }
            this.scrollToBottom();
          }
        } else {
          // 删除等待提示消息
          if (this.waitingMessageIndex !== -1) {
            this.messageList.splice(this.waitingMessageIndex, 1);
            this.waitingMessageIndex = -1;
          }
          // 标记最新患者消息为失败状态
          this.markLatestPatientMessageAsError(question, answer, context, guid);
          this.scrollToBottom();
        }
      } catch (error) {
        console.error('请求失败:', error);
        // 删除等待提示消息
        if (this.waitingMessageIndex !== -1) {
          this.messageList.splice(this.waitingMessageIndex, 1);
          this.waitingMessageIndex = -1;
        }
        // 标记最新患者消息为失败状态
        this.markLatestPatientMessageAsError(question, answer, context, guid);
        // 显示错误提示
        uni.showToast({
          title: '网络请求失败，请重试',
          icon: 'none',
          duration: 2000,
        });
        this.scrollToBottom();
      }
    },
    async onGetDoctorInfo() {
      const res = await getDocInfoMation({
        doctorId: this.doctorId,
      });
      if (res.Type === 200) {
        this.doctorName = res.Data.Doctor.Name;
        this.assistantWeChatQrCode = res.Data.Doctor.AssistantWeChatQrCode;
      }
    },
    // 初始化页面数据
    initPageData() {
      const userInfo = app.globalData.userInfo;
      this.patientAvatar =
        userInfo.HeadImg || '/static/common/default-avatar.png';
      this.patientName = userInfo.Name || '患者';
    },
    async handleSendBaseInfoToServe() {
      await setPreVisitRecord({
        DoctorId: this.doctorId,
        PatientId: app.globalData.userInfo.Id,
        Type: 1, //0=诊后报道 1=预问诊
        BeginTime: this.$dateFormat(new Date(), 'YYYY-MM-DD 00:00:00'), //当天起始时间
        EndTime: this.$dateFormat(new Date(), 'YYYY-MM-DD 23:59:59'), //当天结束时间
      }).then((res) => {
        if (res.Type === 200 && res.Data) {
          this.preVisitRecordId = res.Data;
        } else {
          this.$log.error(
            this.$envVersion,
            `setPreVisitRecord接口出错`,
            res.Content
          );
        }
      });
    },

    // 计算滚动区域高度
    calculateScrollHeight() {
      const systemInfo = uni.getSystemInfoSync();
      const windowHeight = systemInfo.windowHeight;
      // 减去顶部提示区域高度(108rpx转px) 和底部键盘区域高度
      const topHeight = uni.upx2px(108);
      const keyboardAreaHeight = this.isKeyboardShow
        ? uni.upx2px(120) + this.actualKeyboardHeight
        : uni.upx2px(120);

      this.scrollHeight = windowHeight - topHeight - keyboardAreaHeight;
    },

    // 滚动到聊天底部
    scrollToBottom() {
      // this.listScrollTop = 0;
      this.$nextTick(() => {
        // 使用较大的数值确保滚动到底部
        setTimeout(() => {
          this.listScrollTop = this.listScrollTop + 1;
        }, 10);
      });
    },

    // 添加消息
    onSetMessageList() {
      const userInfo = app.globalData.userInfo;
      const currentTime = dayjs(new Date()).format('YYYY-MM-DD HH:mm');

      // 添加患者基本信息消息
      this.messageList.push({
        role: 'patient',
        type: 'text',
        text: userInfo.Name + '，' + userInfo.Sex + '，' + userInfo.Age + '岁',
        sendTime: currentTime,
        showTime: true,
        hasError: false, // 初始化错误状态
        retryParams: null, // 初始化重试参数
      });

      // 添加医生回复消息
      this.messageList.push({
        role: 'doctor',
        type: 'text',
        text: '您好！我是医生小助手，帮您提前将病情传送给医生。请问您这次来看病的主要目的是什么？',
        sendTime: currentTime,
        showTime: false,
        questionId: 1,
        messageType: 'Question',
        sessionId: '00',
      });

      // 添加选择项消息
      this.messageList.push({
        role: 'doctor',
        type: 'objective-options',
        text: '',
        sendTime: currentTime,
        showTime: false,
        options: [
          { text: '初诊(第一次来看这个问题)', selected: false, value: 0 },
          {
            text: '复诊(回来复查/检查报告解读/做治疗开药等)',
            selected: false,
            value: 1,
          },
          { text: '预约/开具检查', selected: false, value: 2 },
          {
            text: '开具医疗证明(病假条/诊断证明/健康证明等)',
            selected: false,
            value: 3,
          },
          { text: '办理手续(转诊/医保审批等)', selected: false, value: 4 },
          { text: '其他', selected: false, value: 5 },
        ],
        questionId: 1,
        messageType: 'options',
      });
    },

    // 处理选择项点击
    async handleOptionClick(option, message, messageIndex) {
      if (this.isSelectObjective) return;
      if (this.sessionId && Object.keys(this.sessionContext).length) return;
      this.isSelectObjective = true;
      // 重置所有选项的选中状态
      message.options.forEach((opt) => {
        opt.selected = false;
      });
      // 将所有医生发送的waiting删除 场景： 当患者先输入了文字，然后在选择类型时，会先发送waiting，然后发送文字，此时需要将waiting删除
      this.messageList = this.messageList.filter(
        (item) => item.type !== 'waiting'
      );

      // 设置当前选项为选中状态
      option.selected = true;

      // 这里可以添加后续逻辑，比如发送选择结果到服务器
      console.log('用户选择了:', option.text);
      const currentTime = dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss');
      const questionTitle =
        '您好！我是医生小助手，帮您提前将病情传送给医生。请问您这次来看病的主要目的是什么？';
      let nextQuestionInfo = {};
      const sessionId = guid();
      switch (option.text) {
        case '初诊(第一次来看这个问题)':
          nextQuestionInfo = {
            Messages: [
              {
                Text: '好的，明白您是初诊。接下来，我需要了解您的症状。',
                Format: 'Text',
                Type: 'Reminder',
                SessionId: sessionId,
              },
              {
                Text: '现在，请描述一下您哪里不舒服，多长时间了？（您本次就诊的主要想解决的问题）比如，左边膝盖痛了两周了。',
                Format: 'Text',
                Type: 'Question',
                SessionId: sessionId,
              },
            ],
            Completed: false,
            Context: null,
          };
          // 将选择的信息类型发送给服务器
          await this.handleGetConsultTypeToService(option.value);
          this.onSendMessageLocal(sessionId, nextQuestionInfo, option.text);
          break;
        case '复诊(回来复查/检查报告解读/做治疗开药等)':
          nextQuestionInfo = {
            Messages: [
              {
                Text: '好的，明白您是来复查，已反馈给医生，请耐心等待。',
                Format: 'Text',
                Type: 'Reminder',
                SessionId: sessionId,
              },
            ],
            Completed: true,
            Context: null,
          };
          // 将选择的信息类型发送给服务器
          await this.handleGetConsultTypeToService(option.value);
          this.onSendMessageLocal(sessionId, nextQuestionInfo, option.text);
          break;
        case '预约/开具检查':
          nextQuestionInfo = {
            Messages: [
              {
                Text: '好的明白，您的需求已反馈给医生，请耐心等待。',
                Format: 'Text',
                Type: 'Reminder',
                SessionId: sessionId,
              },
            ],
            Completed: true,
            Context: null,
          };
          // 将选择的信息类型发送给服务器
          await this.handleGetConsultTypeToService(option.value);
          this.onSendMessageLocal(sessionId, nextQuestionInfo, option.text);
          break;
        case '开具医疗证明(病假条/诊断证明/健康证明等)':
          nextQuestionInfo = {
            Messages: [
              {
                Text: '好的明白，您的需求已反馈给医生，请耐心等待。',
                Format: 'Text',
                Type: 'Reminder',
                SessionId: sessionId,
              },
            ],
            Completed: true,
            Context: null,
          };
          // 将选择的信息类型发送给服务器
          await this.handleGetConsultTypeToService(option.value);
          this.onSendMessageLocal(sessionId, nextQuestionInfo, option.text);
          break;
        case '办理手续(转诊/医保审批等)':
          nextQuestionInfo = {
            Messages: [
              {
                Text: '好的明白，您的需求已反馈给医生，请耐心等待。',
                Format: 'Text',
                Type: 'Reminder',
                SessionId: sessionId,
              },
            ],
            Completed: true,
            Context: null,
          };
          // 将选择的信息类型发送给服务器
          await this.handleGetConsultTypeToService(option.value);
          this.onSendMessageLocal(sessionId, nextQuestionInfo, option.text);
          break;
        case '其他':
          this.isVoiceMode = false;
          this.$nextTick(() => {
            this.textareaFocus = true;
          });
          break;
        default:
          break;
      }
    },
    async handleGetConsultTypeToService(value) {
      await updatePreVisitRecordState({
        Id: this.preVisitRecordId,
        State: value,
      });
    },

    // 处理语音播放功能
    handleVoicePlay(message, messageIndex) {
      console.log('语音播放消息:', message);
      // 如果当前正在播放同一条消息，则停止播放
      if (this.currentPlayingIndex === messageIndex) {
        this.stopVoicePlay();
        return;
      }

      // 检查消息文本是否存在
      if (!message.text || !message.text.trim()) {
        uni.showToast({
          title: '消息内容为空',
          icon: 'none',
          duration: 2000,
        });
        return;
      }

      // 只有在确实有音频正在播放时才停止当前播放
      if (this.currentPlayingIndex !== -1) {
        this.stopVoicePlay();
      }

      // 设置当前播放状态
      this.currentPlayingIndex = messageIndex;

      // 调用微信同声传译插件进行文字转语音
      WechatSI.textToSpeech({
        lang: 'zh_CN',
        content: message.text,
        success: (res) => {
          console.log('TTS成功:', res);
          if (res.retcode !== 0) {
            console.error('TTS转换失败:', res);
            this.currentPlayingIndex = -1;
            uni.showToast({
              title: '语音转换失败',
              icon: 'none',
              duration: 2000,
            });
            return;
          }

          // 播放转换后的音频
          this.innerAudioContext.src = res.filename;
          this.innerAudioContext.play();
        },
        fail: (error) => {
          console.error('TTS调用失败:', error);
          this.currentPlayingIndex = -1;
          uni.showToast({
            title: '语音播放失败',
            icon: 'none',
            duration: 2000,
          });
        },
      });
    },

    // 停止语音播放
    stopVoicePlay() {
      if (this.innerAudioContext) {
        try {
          // 只有在音频实例有src且可能正在播放时才调用stop
          // 避免在未设置src或未开始播放时调用stop导致错误
          if (this.innerAudioContext.src) {
            this.innerAudioContext.stop();
          }
        } catch (error) {
          console.warn('停止音频播放时出错，但不影响功能：', error);
        }
      }
      this.currentPlayingIndex = -1;
    },

    // 初始化音频播放器
    initAudioPlayer() {
      this.innerAudioContext = uni.createInnerAudioContext();

      // 播放开始事件
      this.innerAudioContext.onPlay(() => {
        console.log('开始播放语音');
      });

      // 播放结束事件
      this.innerAudioContext.onEnded(() => {
        console.log('语音播放完成');
        this.currentPlayingIndex = -1;
      });

      // 播放停止事件
      this.innerAudioContext.onStop(() => {
        console.log('语音播放停止');
        this.currentPlayingIndex = -1;
      });

      // 播放错误事件
      this.innerAudioContext.onError((error) => {
        console.error('音频播放错误:', error);
        this.currentPlayingIndex = -1;
        uni.showToast({
          title: '音频播放失败',
          icon: 'none',
          duration: 2000,
        });
      });
    },

    // 处理重新描述功能
    handleRedescribe(message, messageIndex) {
      // 将上一个患者发送消息的sessionId重制到这一次的sessionId 这样操作是为了后续请求回来不用渲染医生回答的数据 （医生回复的sessionId和当前的sessionId不一致了 就不会渲染）
      const copyMessage = JSON.parse(JSON.stringify(this.messageList));
      const beforeMessageList = copyMessage.filter(
        (s) => s.sessionId !== message.sessionId && s.type !== 'waiting'
      );
      if (beforeMessageList && beforeMessageList.length) {
        const lastMessageSessionId =
          beforeMessageList[beforeMessageList.length - 1].sessionId;
        this.sessionId = lastMessageSessionId;
      }
      // 3. 删除该消息之后的所有医生消息，以及该患者消息本身
      // 找到该消息之后的所有消息，删除医生类型的消息
      const messagesToRemove = [];

      // 添加该患者消息本身到删除列表
      messagesToRemove.push(messageIndex);

      // 添加该消息之后的所有医生消息到删除列表
      for (let i = messageIndex + 1; i < this.messageList.length; i++) {
        if (this.messageList[i].role === 'doctor') {
          messagesToRemove.push(i);
        }
      }

      // 从后往前删除，避免索引变化问题
      for (let i = messagesToRemove.length - 1; i >= 0; i--) {
        this.messageList.splice(messagesToRemove[i], 1);
      }

      this.dialogueEnded = false;
      // 获取医生发送的消息（和this.sessionId匹配的 需要查询选择人体图）
      const doctorImageMessageList = this.messageList.filter(
        (s) =>
          s.role === 'doctor' &&
          s.sessionId === this.sessionId &&
          s.type === 'image' &&
          s.text === 'BodySelection'
      );
      if (doctorImageMessageList && doctorImageMessageList.length) {
        this.openBodyPartDialog = true;
        return;
      }
      // 1. 将消息文案放入textarea
      this.inputText = message.text;
      // 2. 切换到文本输入模式
      this.isVoiceMode = false;
      // 4. 聚焦textarea并弹出键盘
      this.$nextTick(() => {
        this.textareaFocus = true;
      });
    },

    // 键盘相关方法
    // 切换输入模式
    toggleInputMode() {
      this.isVoiceMode = !this.isVoiceMode;
    },

    // 输入框获得焦点
    onInputFocus() {
      this.isKeyboardShow = true;
      this.textareaFocus = true;
    },

    // 输入框失去焦点
    onInputBlur() {
      this.isKeyboardShow = false;
      this.actualKeyboardHeight = 0;
      this.textareaFocus = false;
    },

    // 文本输入
    onTextInput(e) {
      this.inputText = e.detail.value;
    },

    // 键盘高度变化
    onKeyboardHeightChange(e) {
      const height = e.detail.height;
      this.actualKeyboardHeight = height;
      this.isKeyboardShow = height > 0;

      // 更新键盘区域高度
      this.keyboardHeight = Math.max(100, height + 20);
      this.calculateScrollHeight();

      // 键盘弹出时滚动到底部
      if (height > 0) {
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }
    },
    getDataBeforeLastDoctor(arr) {
      const result = [];
      for (let i = arr.length - 1; i >= 0; i--) {
        if (arr[i].role === 'doctor') {
          break;
        }
        result.unshift(arr[i]); // 保持原顺序
      }
      return result;
    },

    handleGetLastUserMessage(text) {
      const currentSessionId = this.sessionId;
      const nowText = text || this.inputText;
      let patientSendInputText = '';
      const copyMessageList = JSON.parse(JSON.stringify(this.messageList));
      if (
        copyMessageList.some((s) => s.type === 'waiting') ||
        copyMessageList[copyMessageList.length - 1].role === 'patient' // 这种情况是输入的内容被撤回重新输入
      ) {
        const allMessageList = copyMessageList.filter(
          (v) => v.type !== 'waiting'
        );
        const patientList = this.getDataBeforeLastDoctor(allMessageList);
        if (patientList && patientList.length) {
          const beforeMessages = patientList.map((q) => q.text).join(' ');
          patientSendInputText = beforeMessages + ' ' + nowText;
        } else {
          patientSendInputText = nowText;
        }
      } else {
        patientSendInputText = nowText;
      }
      return patientSendInputText;
    },

    // 发送文本消息
    async sendTextMessage() {
      if (!this.inputText.trim()) return;
      const patientSendInputText = this.handleGetLastUserMessage();
      const currentTime = dayjs(new Date()).format('YYYY-MM-DD HH:mm');
      const uuid = guid();
      // 添加患者消息
      const patientMessage = {
        role: 'patient',
        type: 'text',
        text: this.inputText.trim(),
        sendTime: currentTime,
        showTime: false,
        sessionId: uuid,
        isNew: true,
        hasError: false, // 初始化错误状态
        retryParams: null, // 初始化重试参数
      };
      this.messageList.push(patientMessage);
      this.triggerMessageAnimation(patientMessage);

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom();
      });
      const questions = this.messageList.filter(
        (s) => s.messageType && s.messageType === 'Question'
      );
      const questionTitle = questions[questions.length - 1].text;
      const sessionId = questions[questions.length - 1].sessionId;
      this.handleSendMessageToLocalServeTest(
        questionTitle,
        patientSendInputText,
        this.sessionContext[sessionId] || null,
        uuid
      );

      // 清空输入框
      this.inputText = '';
    },

    // 发送语音识别消息
    sendVoiceMessage(text) {
      if (!text || !text.trim()) return;
      const patientSendInputText = this.handleGetLastUserMessage(text);
      const currentTime = dayjs(new Date()).format('YYYY-MM-DD HH:mm');
      const uuid = guid();

      // 添加患者语音消息
      const patientMessage = {
        role: 'patient',
        type: 'text',
        text: text.trim(),
        sendTime: currentTime,
        showTime: false,
        sessionId: uuid,
        isNew: true,
        isVoiceMessage: true, // 标记为语音消息
        hasError: false, // 初始化错误状态
        retryParams: null, // 初始化重试参数
      };
      this.messageList.push(patientMessage);
      this.triggerMessageAnimation(patientMessage);

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom();
      });

      // 发送到服务器的逻辑
      const questions = this.messageList.filter(
        (s) => s.messageType && s.messageType === 'Question'
      );
      const questionTitle = questions[questions.length - 1].text;
      const sessionId = questions[questions.length - 1].sessionId;
      this.handleSendMessageToLocalServeTest(
        questionTitle,
        patientSendInputText,
        this.sessionContext[sessionId] || null,
        uuid
      );
    },

    // 初始化录音服务
    initRecord() {
      // 有新的识别内容返回，则会调用此事件
      manager.onRecognize = (res) => {
        console.log('onRecognize', res);
        let text = res.result;
        if (text && this.recordingState === 'RECORDING') {
          this.recognizedText = text;
          this.recordText = text || '正在聆听...';
        }
      };

      // 识别结束事件
      manager.onStop = async (res) => {
        console.log(
          'onStop',
          res,
          '当前状态:',
          this.recordingState,
          '是否取消:',
          this.isCancelled
        );

        // 如果是取消状态，不发送消息
        if (this.isCancelled) {
          console.log('录音已取消，不发送消息');
          // 等待一段时间确保资源释放，然后重置录音状态
          await this.waitForRecordingResourceRelease();
          this.resetRecordingState();
          return;
        }

        let text = res.result;
        if (text && text.trim()) {
          // 直接发送识别到的文字消息
          const recognizedText = text.trim();
          console.log('识别结果:', recognizedText);

          // 调用发送消息方法，直接发送语音识别的文字
          this.sendVoiceMessage(recognizedText);
        } else {
          console.log('没有识别到有效内容');
          uni.showToast({
            title: '没有识别到内容，请重试',
            icon: 'none',
            duration: 2000,
          });
        }

        // 等待一段时间确保资源释放，然后重置录音状态
        await this.waitForRecordingResourceRelease();
        this.resetRecordingState();
      };

      // 录音开始事件
      manager.onStart = () => {
        console.log('录音开始，状态:', this.recordingState);
        if (this.recordingState === 'STARTING') {
          this.setRecordingState('RECORDING');
        }
        this.recordText = '正在聆听...';
      };

      // 录音错误事件
      manager.onError = async (res) => {
        console.error('录音错误:', res, '当前状态:', this.recordingState);

        // 检查是否是录音资源冲突错误
        if (res.retcode === -30011) {
          console.log('检测到录音资源冲突，尝试智能重试');

          // 如果是启动时的冲突，尝试重试
          if (this.recordingState === 'STARTING' && this.isLongPressTriggered) {
            const retrySuccess = await this.retryStartRecording();
            if (retrySuccess) {
              this.setRecordingState('RECORDING');
              return; // 重试成功，不显示错误提示
            }
          }
        }

        // 显示错误提示
        uni.showToast({
          title: '录音失败，请重试',
          icon: 'none',
          duration: 2000,
        });

        // 等待资源释放后重置状态
        await this.waitForRecordingResourceRelease();
        this.resetRecordingState();
      };
    },

    // 检查录音权限
    async checkRecordAuth() {
      return new Promise((resolve) => {
        uni.getSetting({
          success: (res) => {
            console.log('checkRecordAuth', res);
            if (res.authSetting['scope.record'] === false) {
              // 用户之前拒绝了权限，需要引导用户去设置页面开启
              uni.showModal({
                title: '提示',
                content: '检测到您没打开录音功能权限，是否去设置打开？',
                confirmText: '确认',
                cancelText: '取消',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    uni.openSetting({
                      success: (settingRes) => {
                        if (settingRes.authSetting['scope.record']) {
                          resolve(true);
                        } else {
                          resolve(false);
                        }
                      },
                      fail: () => resolve(false),
                    });
                  } else {
                    resolve(false);
                  }
                },
              });
            } else if (res.authSetting['scope.record'] === undefined) {
              // 用户还没有授权过，可以直接调用授权
              uni.authorize({
                scope: 'scope.record',
                success: () => resolve(true),
                fail: () => resolve(false),
              });
            } else {
              // 用户已经授权
              resolve(true);
            }
          },
          fail: () => resolve(false),
        });
      });
    },

    // 开始语音录制
    async startVoiceRecord(event) {
      console.log('开始语音录制，当前状态:', this.recordingState);

      // 检查录音状态，如果不是空闲状态则不允许开始新的录音
      if (!this.canStartRecording()) {
        console.log('录音状态不允许开始新的录音:', this.recordingState);
        uni.showToast({
          title: '请等待上次录音完成',
          icon: 'none',
          duration: 1500,
        });
        return;
      }

      // 触发震动效果
      uni.vibrateShort({
        type: 'medium',
      });

      // 记录触摸开始位置
      if (event && event.touches && event.touches[0]) {
        this.touchStartY = event.touches[0].clientY;
        this.touchCurrentY = event.touches[0].clientY;
      }

      // 重置状态
      this.isLongPressTriggered = false;
      this.isInCancelArea = false;
      this.isCancelled = false; // 重置取消标志位

      // 设置长按计时器，500ms后才开始录音
      this.longPressTimer = setTimeout(async () => {
        console.log('长按500ms，准备开始录音');

        // 再次检查录音状态
        if (!this.canStartRecording()) {
          console.log('长按期间录音状态已改变，取消录音');
          return;
        }

        // 设置为启动中状态
        this.setRecordingState('STARTING');

        // 检查录音权限
        const hasAuth = await this.checkRecordAuth();
        if (!hasAuth) {
          uni.showToast({
            title: '需要录音权限才能使用语音功能',
            icon: 'none',
          });
          this.resetRecordingState();
          return;
        }

        // 标记已触发长按
        this.isLongPressTriggered = true;

        this.isRecording = true;
        this.showRecordModal = true;
        this.recordText = '正在聆听...';
        this.recognizedText = '';

        // 开始录音识别
        try {
          manager.start({
            lang: 'zh_CN',
          });
          // 录音启动成功，设置为录音中状态
          this.setRecordingState('RECORDING');
        } catch (error) {
          console.error('录音启动失败:', error);
          // 如果启动失败，尝试智能重试
          const retrySuccess = await this.retryStartRecording();
          if (retrySuccess) {
            this.setRecordingState('RECORDING');
          } else {
            this.resetRecordingState();
          }
        }
      }, 500); // 500ms延迟
    },

    // 处理语音按钮触摸移动
    onVoiceTouchMove(event) {
      if (!this.showRecordModal || !event.touches || !event.touches[0]) {
        return;
      }

      this.touchCurrentY = event.touches[0].clientY;

      // 计算滑动距离（向上滑动为负值）
      const deltaY = this.touchCurrentY - this.touchStartY;

      // 如果向上滑动超过100px，进入取消区域
      const cancelThreshold = -100;
      const wasInCancelArea = this.isInCancelArea;
      this.isInCancelArea = deltaY < cancelThreshold;

      // 如果状态发生变化，触发震动反馈
      if (this.isInCancelArea !== wasInCancelArea) {
        uni.vibrateShort({
          type: 'light',
        });
      }
    },

    // 结束语音录制
    endVoiceRecord() {
      console.log('结束语音录制，当前状态:', this.recordingState);

      // 清除长按计时器
      if (this.longPressTimer) {
        clearTimeout(this.longPressTimer);
        this.longPressTimer = null;
      }

      // 如果没有触发长按（不足500ms），直接返回，不做任何操作
      if (!this.isLongPressTriggered) {
        console.log('长按时间不足500ms，取消录音');
        this.showRecordModal = false;
        this.isInCancelArea = false;
        // 如果状态不是空闲，需要重置状态
        if (this.recordingState !== 'IDLE') {
          this.resetRecordingState();
        }
        return;
      }

      // 如果在取消区域，取消录音
      if (this.isInCancelArea) {
        console.log('在取消区域，取消录音');
        this.cancelVoiceRecord();
        return;
      }

      // 如果正在录音，停止录音并触发震动
      if (this.isRecording && this.recordingState === 'RECORDING') {
        console.log('停止录音...');

        // 设置为停止中状态
        this.setRecordingState('STOPPING');

        // 触发震动效果
        uni.vibrateShort({
          type: 'medium',
        });

        try {
          manager.stop();
        } catch (error) {
          console.error('停止录音失败:', error);
          // 如果停止失败，直接重置状态
          this.resetRecordingState();
        }
      } else if (this.recordingState === 'STARTING') {
        // 如果在启动中状态就结束，直接重置状态
        console.log('录音启动中就结束，重置状态');
        this.resetRecordingState();
      }
    },

    // 取消语音录制
    cancelVoiceRecord() {
      console.log('取消语音录制，当前状态:', this.recordingState);

      // 设置取消标志位
      this.isCancelled = true;

      // 清除长按计时器
      if (this.longPressTimer) {
        clearTimeout(this.longPressTimer);
        this.longPressTimer = null;
      }

      // 如果正在录音，需要先停止录音
      if (this.recordingState === 'RECORDING' && this.isRecording) {
        console.log('取消时停止录音');
        this.setRecordingState('STOPPING');
        try {
          manager.stop();
        } catch (error) {
          console.error('取消时停止录音失败:', error);
          // 如果停止失败，直接重置状态
          this.resetRecordingState();
        }
      } else {
        // 如果不是在录音状态，直接重置状态
        this.resetRecordingState();
      }
    },

    // 关闭遮罩层
    closeMask() {
      // 只有点击遮罩背景时才关闭，身体部位点击不应该关闭
      this.openBodyPartDialog = false;
    },

    // 处理身体部位点击
    handlePointClick(extId, event) {
      console.log('点击身体部位:', extId);

      // 只阻止事件冒泡，不阻止默认行为，确保不会触发遮罩关闭
      if (event) {
        event.stopPropagation();
      }

      // 找到对应的身体部位并切换选中状态
      const pointIndex = this.bodyPoints.findIndex(
        (point) => point.Ext === extId
      );
      if (pointIndex !== -1) {
        // 使用 $set 确保响应式更新
        this.$set(
          this.bodyPoints[pointIndex],
          'IsSelect',
          !this.bodyPoints[pointIndex].IsSelect
        );

        console.log('身体部位选中状态已更新:', this.bodyPoints[pointIndex]);
      }
    },

    // 确认选择
    confirmSelection() {
      // 获取所有选中的身体部位
      const selectedPoints = this.bodyPoints.filter((point) => point.IsSelect);
      if (!selectedPoints.length) {
        uni.showToast({
          title: '请选择至少一个身体部位',
          icon: 'none',
        });
        return;
      }

      // 可以在这里添加后续的消息处理逻辑
      const currentTime = dayjs(new Date()).format('YYYY-MM-DD HH:mm');
      // 构建选中部位的描述文本
      const selectedNames = selectedPoints
        .map((point) => {
          // 优先使用 PositiveName，如果没有则使用 BackName，都没有则使用 Ext 编号
          return point.PositiveName || point.BackName || `部位${point.Ext}`;
        })
        .join('、');
      this.inputText = selectedNames;
      this.sendTextMessage();
      this.openBodyPartDialog = false;
    },

    // 触发消息动画
    triggerMessageAnimation(message) {
      // 确保DOM已经渲染，然后立即触发动画
      this.$nextTick(() => {
        // 使用短延时确保在下一帧移除动画类，兼容微信小程序
        setTimeout(() => {
          message.isNew = false;
        }, 16); // 约一帧的时间（60fps = 16.67ms）
      });
    },

    // ============ 录音状态管理辅助方法 ============

    // 检查是否可以开始录音
    canStartRecording() {
      return this.recordingState === 'IDLE';
    },

    // 设置录音状态
    setRecordingState(state) {
      console.log(`录音状态变更: ${this.recordingState} -> ${state}`);
      this.recordingState = state;
    },

    // 重置录音状态到空闲
    resetRecordingState() {
      this.setRecordingState('IDLE');
      this.recordRetryCount = 0;
      this.isRecording = false;
      this.showRecordModal = false;
      this.recordText = '正在聆听...';
      this.recognizedText = '';
      this.isLongPressTriggered = false;
      this.isInCancelArea = false;
      this.isCancelled = false; // 重置取消标志位
      this.touchStartY = 0;
      this.touchCurrentY = 0;
    },

    // 延迟等待录音资源释放
    async waitForRecordingResourceRelease() {
      return new Promise((resolve) => {
        // 等待足够长的时间确保录音资源完全释放
        setTimeout(resolve, 300);
      });
    },

    // 智能重试录音开始
    async retryStartRecording() {
      if (this.recordRetryCount >= this.maxRecordRetries) {
        console.log('录音重试次数已达上限，放弃重试');
        uni.showToast({
          title: '录音启动失败，请稍后重试',
          icon: 'none',
          duration: 2000,
        });
        this.resetRecordingState();
        return false;
      }

      this.recordRetryCount++;
      console.log(`第 ${this.recordRetryCount} 次重试录音`);

      // 设置重试提示
      this.recordText = `正在重试录音(${this.recordRetryCount}/${this.maxRecordRetries})...`;

      // 等待一段时间后重试
      await this.waitForRecordingResourceRelease();

      // 确保UI状态正确
      if (!this.showRecordModal) {
        this.showRecordModal = true;
      }

      try {
        // 设置为启动中状态
        this.setRecordingState('STARTING');

        manager.start({
          lang: 'zh_CN',
        });

        // 重置录音提示文本
        this.recordText = '正在聆听...';
        return true;
      } catch (error) {
        console.error(`录音重试失败:`, error);

        // 如果重试失败，继续尝试下一次重试
        if (this.recordRetryCount < this.maxRecordRetries) {
          return await this.retryStartRecording();
        }

        return false;
      }
    },

    // 标记最新患者消息为错误状态
    markLatestPatientMessageAsError(question, answer, context, guid) {
      // 找到最新的患者消息
      for (let i = this.messageList.length - 1; i >= 0; i--) {
        if (
          this.messageList[i].role === 'patient' &&
          this.messageList[i].sessionId === guid
        ) {
          // 标记错误状态并保存重试参数
          this.$set(this.messageList[i], 'hasError', true);
          this.$set(this.messageList[i], 'retryParams', {
            question,
            answer,
            context,
            guid,
          });
          break;
        }
      }
    },

    // 处理重试消息
    async handleRetryMessage(message, messageIndex) {
      if (!message.retryParams) {
        console.error('重试参数不存在');
        return;
      }

      // 先保存重试参数
      const { question, answer, context, guid } = message.retryParams;

      // 清除错误状态
      this.$set(message, 'hasError', false);
      this.$set(message, 'retryParams', null);

      // 重新发送请求
      await this.handleSendMessageToLocalServeTest(
        question,
        answer,
        context,
        guid
      );
    },
  },
};
</script>
<style lang="scss" scoped>
/deep/ .u-divider {
  margin: 0 !important;
}
.spinal-overlay-warp {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  height: 100%;
  &-container {
    width: 580rpx;
    background: linear-gradient(0deg, #ffffff 0%, #f8fffe 76%, #e2fffc 100%);
    box-shadow: 0rpx -2rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
    border-radius: 0 0 24rpx 24rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 54rpx;
    &-banner {
      width: 322rpx;
      height: 314rpx;
      background-image: url('https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/index-overlay-banner.png');
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center center;
      display: flex;
      justify-content: center;
      align-items: center;
      &-image {
        width: 260rpx;
        height: 260rpx;
      }
    }
    &-texttop {
      font-size: 30rpx;
      color: #181818;
      margin-top: 40rpx;
      margin-bottom: 54rpx;
    }
    &-text {
      font-weight: 400;
      font-size: 26rpx;
      color: #999999;
      margin-bottom: 70rpx;
      text-align: center;
    }
    &-button {
      width: 300rpx;
      height: 64rpx;
      background: #29b7a3;
      border-radius: 32rpx;
      font-weight: 600;
      font-size: 32rpx;
      color: #ffffff;
      line-height: 64rpx !important;
      margin-top: 46rpx;
    }
  }
}
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  &-user {
    margin: 0 auto;
    margin-top: 32rpx;
    width: calc(100% - 64rpx);
    background: #ffffff;
    box-shadow: 0rpx 3rpx 8rpx 0rpx rgba(41, 183, 163, 0.08);
    border-radius: 16rpx;
    padding: 0 32rpx;
    &-line {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 102rpx;
      &-right {
        flex: 1;
      }
      &-input {
        text-align: right;
      }
      &-sex {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        &-item {
          width: 96rpx;
          height: 56rpx;
          background: #ffffff;
          border-radius: 27rpx;
          border: 2px solid #eeeeee;
          text-align: center;
          font-size: 28rpx;
          color: #667a7d;
          margin-left: 18rpx;
          line-height: 54rpx;
        }
        &-select {
          background: #29b7a3;
          color: #ffffff;
        }
      }
    }
  }

  &-top {
    height: 108rpx;
    background: #f0fffc;
    font-weight: 500;
    font-size: 26rpx;
    color: #39ccaf;
    padding: 20rpx 32rpx;
    display: flex;
    align-items: center;
  }

  &-chat {
    flex: 1;
    background: #f7f7f7;
    overflow: hidden;
  }

  &-keyboard {
    min-height: 120rpx;
    background: #ffffff;
    border-top: 1rpx solid #e5e5e5;
    padding: 20rpx 26rpx 40rpx 26rpx;
    display: flex;
    align-items: flex-end;
    position: relative;
    z-index: 100;
  }
}

// 聊天消息样式
.session-custom-cell {
  margin-bottom: 32rpx;
}

// 消息动画样式
.message-animation-left {
  // 默认状态：从左侧隐藏
  opacity: 0;
  transform: translateX(-100rpx);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  // 当不是新消息时，显示正常状态
  &:not(.message-enter) {
    opacity: 1;
    transform: translateX(0);
  }
}

.message-animation-right {
  // 默认状态：从右侧隐藏
  opacity: 0;
  transform: translateX(100rpx);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  // 当不是新消息时，显示正常状态
  &:not(.message-enter) {
    opacity: 1;
    transform: translateX(0);
  }
}

.time-view {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 16rpx 0;

  text {
    font-size: 28rpx;
    color: #999999;
  }
}

.session-body-view {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  margin-bottom: 16rpx;

  &--right {
    flex-direction: row-reverse;
  }
}

.session-content-view {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-left: 16rpx;
  flex: 1;

  &--right {
    align-items: flex-end;
    margin-left: 0;
    margin-right: 16rpx;
  }
}

.name {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.session-content-body-view {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  width: 100%;

  &--right {
    flex-direction: row-reverse;
  }
}

.session-custom-text-sender-cell {
  background: #29b7a3;
  border-radius: 16rpx;
  padding: 24rpx;
  max-width: 500rpx;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    right: -12rpx;
    top: 20rpx;
    width: 0;
    height: 0;
    border-left: 12rpx solid #29b7a3;
    border-top: 8rpx solid transparent;
    border-bottom: 8rpx solid transparent;
  }
}

.session-custom-text-receiver-cell {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  max-width: 500rpx;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

  &::before {
    content: '';
    position: absolute;
    left: -12rpx;
    top: 20rpx;
    width: 0;
    height: 0;
    border-right: 12rpx solid #ffffff;
    border-top: 8rpx solid transparent;
    border-bottom: 8rpx solid transparent;
  }
}

.custom-text {
  font-size: 32rpx;
  color: #333333;
  line-height: 1.5;
  word-wrap: break-word;
  word-break: break-all;
}

.session-custom-text-sender-cell .custom-text {
  color: #ffffff;
}

// 等待提示消息样式
.waiting-message {
  background: #f0f9ff !important;
  border: 1px solid #e0f2fe !important;

  &::before {
    border-right-color: #f0f9ff !important;
  }
}

.waiting-content {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.waiting-dots {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.waiting-dots .dot {
  width: 8rpx;
  height: 8rpx;
  background: #29b7a3;
  border-radius: 50%;
  animation: waiting-bounce 1.4s ease-in-out infinite both;
}

.waiting-dots .dot:nth-child(1) {
  animation-delay: -0.32s;
}

.waiting-dots .dot:nth-child(2) {
  animation-delay: -0.16s;
}

.waiting-dots .dot:nth-child(3) {
  animation-delay: 0s;
}

.waiting-text {
  font-size: 28rpx;
  color: #29b7a3;
  line-height: 1.4;
}

@keyframes waiting-bounce {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

// 重新描述按钮样式
.redescribe-button {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 8rpx;
  padding: 8rpx 0;
  transition: all 0.3s ease;

  &:active {
    opacity: 0.7;
  }
}

.redescribe-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.redescribe-text {
  font-size: 24rpx;
  color: #666666;
  line-height: 1;
}

// 错误重试图标样式
.error-retry-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
  border-radius: 50%;
  background: rgba(255, 59, 48, 0.1);
  border: 1rpx solid #ff3b30;
  transition: all 0.3s ease;

  &:active {
    opacity: 0.7;
    transform: scale(0.95);
  }
}

.error-icon {
  width: 28rpx;
  height: 28rpx;
  tint-color: #ff3b30;
}

// 语音播放按钮样式
.voice-play-button {
  width: 178rpx;
  height: 60rpx;
  background: #ffffff;
  border-radius: 8rpx;
  border: 1px solid #29b7a3;
  padding: 16rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-top: 8rpx;

  &--playing {
    .voice-play-text {
      color: #ff6b6b;
      font-weight: 500;
    }

    .voice-play-icon {
      // animation: voice-playing 1s ease-in-out infinite;
      width: 28rpx;
      height: 28rpx;
    }
  }
}

.voice-play-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}

.voice-play-text {
  font-size: 24rpx;
  color: #29b7a3;
  line-height: 1;
}

// 选择项样式
.options-container {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.option-item {
  border: 2rpx solid #e9ecef;
  padding: 16rpx 20rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f5f6fa;
  border-radius: 8rpx;
  color: #333333;

  &--selected {
    background: #dff4f1;
    border: 1px solid #29b7a3;

    .option-text {
      color: #29b7a3;
      font-weight: 500;
    }
  }
}

.option-text {
  font-size: 30rpx;
  color: #333333;
  line-height: 1.4;
}

// 键盘输入区域样式 - 严格按照UI设计
.voice-mode {
  display: flex;
  align-items: flex-end;
  width: 100%;
  gap: 20rpx;
}

.voice-toggle-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.voice-icon {
  width: 56rpx;
  height: 56rpx;
}

.voice-button {
  flex: 1;
  height: 80rpx;
  background: #f8f7f7;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #e0e0e0;

  &--recording {
    background: #29b7a3;
    border-color: #29b7a3;

    .voice-text {
      color: #ffffff;
    }
  }
}

.voice-text {
  font-size: 32rpx;
  color: #333333;
  font-weight: 400;
}

.text-mode {
  display: flex;
  align-items: flex-end;
  width: 100%;
  gap: 20rpx;
}

.text-toggle-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.input-wrapper {
  flex: 1;
  min-height: 80rpx;
  max-height: 200rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  border: 2rpx solid #e0e0e0;
  display: flex;
  align-items: flex-start;
  padding: 16rpx 12rpx;
}

.text-input {
  width: 100%;
  min-height: 40rpx;
  border: none;
  outline: none;
  background: transparent;
  font-size: 32rpx;
  color: #333333;
  line-height: 1.4;
  resize: none;
}

.confirm-button {
  width: 120rpx;
  height: 80rpx;
  background: #cccccc;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  &--active {
    background: #29b7a3;

    .confirm-text {
      color: #ffffff;
    }
  }
}

.confirm-text {
  font-size: 32rpx;
  color: #999999;
  font-weight: 400;
}

// 录音弹窗样式 - 参考微信设计
.record-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.record-content {
  width: 400rpx;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 24rpx;
  padding: 60rpx 40rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.record-animation {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.record-circle {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: #29b7a3;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  animation: record-pulse 2s ease-in-out infinite;
}

.record-inner-circle {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.record-mic-icon {
  width: 40rpx;
  height: 40rpx;
}

.record-waves {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.record-waves .wave {
  position: absolute;
  width: 120rpx;
  height: 120rpx;
  border: 4rpx solid rgba(41, 183, 163, 0.3);
  border-radius: 50%;
  animation: wave-expand 2s ease-out infinite;
}

.record-waves .wave:nth-child(1) {
  animation-delay: 0s;
}

.record-waves .wave:nth-child(2) {
  animation-delay: 0.7s;
}

.record-waves .wave:nth-child(3) {
  animation-delay: 1.4s;
}

.record-text {
  font-size: 32rpx;
  color: #ffffff;
  text-align: center;
  margin-bottom: 20rpx;
  min-height: 44rpx;
  line-height: 44rpx;
}

.record-tip {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

// 取消区域样式
.cancel-area {
  position: absolute;
  bottom: -120rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  opacity: 0.6;
}

.cancel-area--active {
  background: rgba(255, 59, 48, 0.8);
  border-color: #ff3b30;
  opacity: 1;
  transform: translateX(-50%) scale(1.1);
}

.cancel-icon {
  font-size: 40rpx;
  color: #ffffff;
  font-weight: bold;
  line-height: 1;
}

.cancel-text {
  font-size: 20rpx;
  color: #ffffff;
  margin-top: 8rpx;
  text-align: center;
}

// 取消状态下的录音圆圈样式
.record-circle--cancel {
  background: #ff3b30 !important;
  animation: record-cancel-pulse 1s ease-in-out infinite !important;
}

@keyframes record-cancel-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 59, 48, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 20rpx rgba(255, 59, 48, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 59, 48, 0);
  }
}

@keyframes record-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(41, 183, 163, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 20rpx rgba(41, 183, 163, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(41, 183, 163, 0);
  }
}

@keyframes wave-expand {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(2.5);
    opacity: 0;
  }
}

@keyframes voice-playing {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// 遮罩层样式
.container-bodyDialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
}

.mask-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.mask-content {
  width: 100%;
  height: 550px;
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  animation: mask-slide-up 0.3s ease-out;
  display: flex;
  flex-direction: column;
}

.mask-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 0 32rpx;
  margin-bottom: 32rpx;
}

.mask-title {
  font-size: 30rpx;
  font-weight: 400;
  color: #222222;
}

.mask-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
}

.close-text {
  font-size: 40rpx;
  color: #666666;
  line-height: 1;
}

.mask-body {
  position: relative;
  padding: 0 32rpx 32rpx;
  flex: 1;
  &-img {
    width: 100%;
    height: 100%;
  }
  &-point {
    position: absolute;
    width: 56rpx;
    height: 56rpx;
    z-index: 9;
  }
}

.mask-description {
  font-size: 28rpx;
  color: #666666;
  text-align: center;
  margin-bottom: 40rpx;
  display: block;
}

.body-diagram {
  width: 100%;
  height: 400rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #ddd;
}

.placeholder-text {
  font-size: 28rpx;
  color: #999999;
}

.mask-footer {
  padding: 0 32rpx 32rpx;
}

.confirm-btn {
  width: 100%;
  height: 88rpx;
  background: #29b7a3;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:active {
    background: #248a7a;
    transform: scale(0.98);
  }
}

.btn-text {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 500;
}

@keyframes mask-slide-up {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>
