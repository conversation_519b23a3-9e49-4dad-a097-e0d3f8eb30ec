<template>
  <view class="container">
    <u-navbar
      titleStyle="color: #FFFFFF"
      leftIconColor="#FFFFFF"
      :placeholder="false"
      title="评估报告"
      @leftClick="onLeftClick"
      :safeAreaInsetTop="true"
      :fixed="true"
      bgColor="rgba(0, 0, 0, 0)"
    >
    </u-navbar>
    <image src="static/head_icon.png" class="container-top"></image>
    <view class="container-box">
      <view class="container-box-user">
        <image class="container-box-user-img" :src="userInfo.HeadImg"></image>
        <view class="container-box-user-userInfo">
          <text style="font-weight: 500; font-size: 36rpx; color: #333333">{{
            userInfo.UserName
          }}</text>
          <text
            style="
              font-weight: 500;
              font-size: 24rpx;
              color: #333333;
              margin-left: 20rpx;
            "
            >{{ userInfo.Sex }}</text
          >
          <text
            style="
              font-weight: 500;
              font-size: 24rpx;
              color: #333333;
              margin-left: 24rpx;
            "
            >{{ userInfo.Age }}</text
          >
          <view
            class="container-box-user-userInfo-label"
            style="margin-top: 20rpx"
          >
            报告生成时间：{{ userInfo.CreatedTime || '暂无' }}
          </view>
        </view>
      </view>
      <!-- 认知功能 -->
      <block v-if="cognitiveList.length || brainReportList.length">
        <view class="container-box-gauge">
          <view class="container-box-gauge-title">
            <image
              src="static/left_icon.png"
              class="container-box-gauge-title-icon"
            ></image>
            <text class="container-box-gauge-title-name">认知功能</text>
          </view>
          <view class="container-box-gauge-list">
            <view
              v-if="brainReportList.length"
              style="
                display: flex;
                align-items: center;
                justify-content: space-between;
                flex-wrap: wrap;
              "
            >
              <view
                v-for="(item, index) in brainReportList"
                :key="index"
                class="container-box-gauge-report container-box-gauge-brain"
                @click="handleGetBrainReport(item)"
              >
                <image
                  src="static/icon-brain.png"
                  class="container-box-gauge-report-icon"
                />
                <text class="container-box-gauge-report-text"
                  >认知测验报告</text
                >
              </view>
            </view>
            <block v-if="cognitiveList.length">
              <view
                v-for="(item, index) in cognitiveList"
                :key="index"
                class="container-box-gauge-list-item"
                @click="handleSeeDetail(item.BaseGaugeId, item.ReportId)"
              >
                <view class="container-box-gauge-list-item-top">
                  <view class="container-box-gauge-list-item-top-icon"></view>
                  <text class="container-box-gauge-list-item-top-name">{{
                    handleGetName(item.Sign)
                  }}</text>
                  <text
                    class="container-box-gauge-list-item-top-score"
                    v-if="handleGetIsScore(item.Sign)"
                    >{{ item.Report[0].Report[0].Result }}</text
                  >
                </view>
                <view class="container-box-gauge-list-item-content">
                  <text class="container-box-gauge-list-item-content-problem">{{
                    onGetConclusion(item.Sign, item.Report)
                  }}</text>
                  <text
                    class="container-box-gauge-list-item-content-recommendations"
                    >{{ onGetSuggest(item.Sign, item.Report) }}</text
                  >
                </view>
              </view>
            </block>
          </view>
        </view>
      </block>
      <!-- 运动功能 -->
      <block
        v-if="motionList.length || groupList.length || gaitReportList.length"
      >
        <view class="container-box-gauge">
          <view class="container-box-gauge-title">
            <image
              src="static/left_icon.png"
              class="container-box-gauge-title-icon"
            ></image>
            <text class="container-box-gauge-title-name">运动功能</text>
          </view>
          <view class="container-box-gauge-list">
            <view
              v-if="gaitReportList.length"
              style="
                display: flex;
                align-items: center;
                justify-content: space-between;
                flex-wrap: wrap;
              "
            >
              <view
                v-for="(item, index) in gaitReportList"
                :key="index"
                class="container-box-gauge-report container-box-gauge-gaie"
                @click="handleGetBrainReport(item)"
              >
                <image
                  src="static/icon-gait-same.png"
                  class="container-box-gauge-report-icon"
                />
                <text class="container-box-gauge-report-text"
                  >步态测验报告</text
                >
              </view>
            </view>
            <block v-if="motionList.length">
              <view
                v-for="(item, index) in motionList"
                :key="index"
                class="container-box-gauge-list-item"
                @click="handleSeeDetail(item.BaseGaugeId, item.ReportId)"
              >
                <view class="container-box-gauge-list-item-top">
                  <view class="container-box-gauge-list-item-top-icon"></view>
                  <text class="container-box-gauge-list-item-top-name">{{
                    handleGetName(item.Sign)
                  }}</text>
                  <text
                    class="container-box-gauge-list-item-top-score"
                    v-if="handleGetIsScore(item.Sign)"
                    >{{ item.Report[0].Report[0].Result }}</text
                  >
                </view>
                <view class="container-box-gauge-list-item-content">
                  <text class="container-box-gauge-list-item-content-problem">{{
                    onGetConclusion(item.Sign, item.Report)
                  }}</text>
                  <text
                    class="container-box-gauge-list-item-content-recommendations"
                    >{{ onGetSuggest(item.Sign, item.Report) }}</text
                  >
                </view>
              </view>
            </block>
            <block v-if="groupList.length">
              <view
                v-for="(item, index) in groupList"
                :key="index"
                class="container-box-gauge-list-item"
                @click="handleSeeDetail(item.BaseGaugeId, item.ReportId)"
              >
                <view class="container-box-gauge-list-item-top">
                  <view class="container-box-gauge-list-item-top-icon"></view>
                  <text class="container-box-gauge-list-item-top-name">{{
                    handleGetName(item.Sign)
                  }}</text>
                  <!-- <text class="container-box-gauge-list-item-top-score"
                      v-if="handleGetIsScore(item.Sign)">{{item.Report[0].Report[0].Result}}</text> -->
                </view>
                <view class="container-box-gauge-list-item-content">
                  <block
                    v-for="(o, index) in handleGetGroupReportList(item.Report)"
                    :key="index"
                  >
                    <text
                      class="container-box-gauge-list-item-content-problem"
                      >{{ onGetGroupConclusion(o.ShowName, o.Report) }}</text
                    >
                  </block>
                  <text
                    class="container-box-gauge-list-item-content-recommendations"
                    >{{ onGetGroupSuggest(item.Report) }}</text
                  >
                </view>
              </view>
            </block>
          </view>
        </view>
      </block>
      <!-- <block v-if="groupList.length">
        <view class="container-box-gauge">
          <view class="container-box-gauge-title">
            <image
              src="static/left_icon.png"
              class="container-box-gauge-title-icon"
            ></image>
            <text class="container-box-gauge-title-name">运动功能</text>
          </view>
          <view class="container-box-gauge-list">
            <block v-for="(item, index) in groupList" :key="index">
              <view
                class="container-box-gauge-list-item"
                @click="handleSeeDetail(item.BaseGaugeId, item.ReportId)"
              >
                <view class="container-box-gauge-list-item-top">
                  <view class="container-box-gauge-list-item-top-icon"></view>
                  <text class="container-box-gauge-list-item-top-name">{{
                    handleGetName(item.Sign)
                  }}</text>
                </view>
                <view class="container-box-gauge-list-item-content">
                  <block
                    v-for="(o, index) in handleGetGroupReportList(item.Report)"
                    :key="index"
                  >
                    <text
                      class="container-box-gauge-list-item-content-problem"
                      >{{ onGetGroupConclusion(o.ShowName, o.Report) }}</text
                    >
                  </block>
                  <text
                    class="container-box-gauge-list-item-content-recommendations"
                    >{{ onGetGroupSuggest(item.Report) }}</text
                  >
                </view>
              </view>
            </block>
          </view>
        </view>
      </block> -->
    </view>
  </view>
</template>

<script>
const app = getApp();
import dayjs from 'dayjs';
import { getRiskWarningWriteReport } from '@/api/tenant.js';
import riskWarningManager from '@/services/RiskWarningManager.js';
import { getConsultRecordInfo } from '@/api/consult.js';
import { getGaugeReport, gaitQueryReport } from '@/api/supplier.js';
import { getPatientInfoByProgramId } from '@/api/training.js';
export default {
  data() {
    return {
      reportId: '',
      userInfo: {},
      cognitiveList: [],
      motionList: [],
      settingList: [],
      groupList: [],
      consultId: '',
      programId: '',
      brainReportList: [],
      gaitReportList: [],
    };
  },
  async onLoad(option) {
    this.reportId = option.reportId;
    await this.handleGetSetting();
    this.onGetReport();
    if (option.consultId) {
      this.consultId = option.consultId;
    }
    if (option.programId) {
      this.programId = option.programId;
    }
    this.onGetAllPDFReport();
  },
  methods: {
    async onGetAllPDFReport() {
      try {
        let startTime = '';
        let endTime = '';
        let userId = '';
        if (this.consultId && this.consultId !== 'undefined') {
          const { Data } = await getConsultRecordInfo(this.consultId);
          if (Data) {
            endTime = Data.Consult.CompletedTime || new Date();
            startTime = this.$dateFormat(
              Data.Consult.CreateDate,
              'YYYY-MM-DD HH:mm:ss'
            );
            userId = Data.Consult.UserId;
          }
        }
        if (this.programId && this.programId !== 'undefined') {
          const { Data } = await getPatientInfoByProgramId({
            programId: this.programId,
          });
          if (Data) {
            userId = Data.UserInfo.Id;
            startTime = this.$dateFormat(
              Data.TrainingProgram?.StartTime,
              'YYYY-MM-DD HH:mm:ss'
            );
            endTime = Data.TrainingProgram?.ActualFinishedTime
              ? Data.TrainingProgram?.ActualFinishedTime
              : Data.TrainingProgram?.FinishedTime;
          }
        }
        // 获取脑洞报告
        this.onGetBrainReportList(startTime, endTime);
        // 获取步态报告
        this.onGetGaitReportList(startTime, endTime);
      } catch (err) {
        console.error('出错了：', err);
      }
    },
    async onGetGaitReportList(startTime, endTime) {
      const res = await gaitQueryReport({
        Phone: '18081344235' || app.globalData.userInfo.PhoneNumber || null,
        Name: '小谯' || app.globalData.userInfo.Name || null,
        StartTime: '2025-06-15' || startTime,
        EndTime: dayjs(endTime).add(1, 'hour').format('YYYY-MM-DD HH:00:00'),
      });
      if (res.Type === 200 && res.Data.Data.length) {
        this.gaitReportList = res.Data.Data;
      }
    },
    async onGetBrainReportList(startTime, endTime) {
      const res = await getGaugeReport({
        userId: app.globalData.userInfo.Id,
        startTime,
        endTime: dayjs(endTime).add(1, 'hour').format('YYYY-MM-DD HH:00:00'),
      });
      this.brainReportList = res.Data.Data;
    },
    handleGetBrainReport(item) {
      let pdfFile = item.PDFUrl || item.FileUrl;
      const deviceInfo = uni.getDeviceInfo();
      const baseUrl = pdfFile.split('?')[0];
      if (
        deviceInfo.platform === 'android' &&
        baseUrl.toLowerCase().endsWith('.pdf')
      ) {
        uni.showLoading({
          title: '',
          mask: true,
        });
        uni.downloadFile({
          url: pdfFile,
          success: (res) => {
            if (res.statusCode != 200) {
              uni.showToast({
                icon: 'none',
                title: res.errMsg ?? '获取报告失败',
              });
              return;
            }
            uni.openDocument({
              filePath: res.tempFilePath,
              success: () => {
                console.log('打开文档成功');
              },
              fail: (err) => {
                console.log('打开文档失败', err);
              },
            });
          },
          complete: () => {
            uni.hideLoading();
          },
        });
      } else {
        uni.navigateTo({
          url:
            '/subReport/WebView?Url=' +
            encodeURIComponent(pdfFile) +
            '&IsShowBack=' +
            false,
        });
      }
    },
    onGetGroupSuggest(reportList) {
      if (!reportList || !reportList.length) {
        return '';
      }
      const mapList = reportList
        .map((s) => s.Report)
        .flat()
        .map((v) => v.Suggest)
        .filter((suggest) => suggest);
      return mapList.join('');
    },
    onGetGroupConclusion(showName, reportList) {
      let str = showName;
      if (reportList && reportList.length) {
        str += ':';
        reportList.forEach((s) => {
          str += (s.Result || '暂无') + ' ';
        });
      }
      return str;
    },
    handleGetGroupReportList(reportList) {
      if (!reportList || !reportList.length) {
        return [];
      }
      return reportList;
    },
    handleGetName(sign) {
      return (
        riskWarningManager && riskWarningManager.getRiskConfigBySign(sign).Name
      );
    },
    handleGetIsScore(sign) {
      return riskWarningManager && riskWarningManager.isScore(sign);
    },
    async handleGetSetting() {
      const res = await riskWarningManager.requestRiskWarningConfig();
      if (res.Type === 200) {
        this.settingList = res.Data;
      }
    },
    handleSeeDetail(baseGaugeId, reportId) {
      if (!baseGaugeId || !reportId) {
        return;
      }
      uni.navigateTo({
        // 这里FillType写1 只是为了不显示重新评估这个按钮
        url: `/subRiskWarning/historyGaugeList?BaseGaugeId=${baseGaugeId}&ReportId=${reportId}&FillType=1`,
      });
    },
    onGetConclusion(sign, reportList) {
      if (riskWarningManager.isGroup(sign)) {
        return;
      }
      if (riskWarningManager.isConclusion(sign)) {
        const allResult = reportList
          .map((s) => s.Report)
          .flat()
          .map((s) => s.Result)
          .join('');
        return allResult;
      }
      return reportList && reportList.length ? reportList[0].Conclusion : '';
    },
    onGetSuggest(sign, reportList) {
      if (riskWarningManager.isConclusion(sign)) {
        const allSuggest = reportList
          .map((s) => s.Report)
          .flat()
          .map((s) => s.Suggest)
          .join('');
        return allSuggest;
      }
      return reportList && reportList.length ? reportList[0].ShowTips : '';
    },
    async onGetReport() {
      const res = await getRiskWarningWriteReport({
        UserId: app.globalData.userInfo.Id,
        ReportId: this.reportId,
      });
      if (res.Type === 200 && res.Data && res.Data.Report.length) {
        const cognitiveList = res.Data.Report.filter(
          (s) =>
            s.Sign !== 1 &&
            s.Sign !== 2 &&
            riskWarningManager.isCognition(s.Sign)
        );
        const motionList = res.Data.Report.filter(
          (s) =>
            s.Sign !== 1 && s.Sign !== 2 && riskWarningManager.isMotion(s.Sign)
        );
        const groupList = res.Data.Report.filter(
          (s) =>
            s.Sign !== 1 && s.Sign !== 2 && riskWarningManager.isGroup(s.Sign)
        );
        this.cognitiveList = cognitiveList;
        this.motionList = motionList;
        this.groupList = groupList;
        this.userInfo = {
          UserName: res.Data.UserName,
          Sex: res.Data.Sex,
          Age: res.Data.Age,
          CreatedTime: this.$dateFormat(
            res.Data.CreatedTime,
            'YYYY-MM-DD HH:mm:ss',
            false
          ),
          HeadImg: res.Data.HeadImg,
        };
      } else {
        this.userInfo = {
          UserName: app.globalData.userInfo.Name,
          Sex: app.globalData.userInfo.Sex,
          Age: app.globalData.userInfo.Age,
          HeadImg: app.globalData.userInfo.HeadImg,
        };
      }
    },
    onLeftClick() {
      let pages = getCurrentPages();
      if (pages.length > 1) {
        uni.navigateBack();
      } else {
        uni.reLaunch({
          url: '/pages/index/index',
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .u-navbar__content {
  z-index: 9;
}

.container {
  &-top {
    width: 100%;
    height: 336rpx;
  }

  &-box {
    width: 100%;
    padding: 0 32rpx;
    height: 500rpx;
    transform: translateY(-132rpx);

    &-user {
      padding: 0 32rpx;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      height: 210rpx;
      background: white;
      box-shadow: 0rpx 2rpx 10rpx 0rpx rgba(41, 183, 163, 0.1);
      border-radius: 24rpx;

      &-img {
        width: 106rpx;
        height: 106rpx;
        border-radius: 50%;
      }

      &-userInfo {
        flex: 1;
        margin-left: 20rpx;

        &-label {
          font-weight: 400;
          font-size: 24rpx;
          color: #666666;
        }
      }
    }

    &-gauge {
      margin-top: 42rpx;
      &-report {
        width: 300rpx;
        height: 110rpx;
        border-radius: 20rpx;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding: 0 18rpx;
        margin-bottom: 12rpx;
        &-icon {
          width: 64rpx;
          height: 64rpx;
          margin-right: 12rpx;
        }
        &-text {
          font-weight: 500;
          font-size: 30rpx;
          color: #333333;
          line-height: 34rpx;
          flex: 1;
        }
      }
      &-brain {
        background: #e3f9f7;
      }
      &-gaie {
        background: #e3f1fa;
      }
      &-title {
        display: flex;
        align-items: center;
        justify-content: flex-start;

        &-icon {
          width: 24rpx;
          height: 24rpx;
        }

        &-name {
          font-weight: 600;
          font-size: 34rpx;
          color: #333333;
          margin-left: 18rpx;
          flex: 1;
        }
      }

      &-list {
        margin-top: 32rpx;
        background: #ffffff;
        box-shadow: 0rpx 2rpx 10rpx 0rpx rgba(41, 183, 163, 0.1);
        border-radius: 24rpx;
        padding: 32rpx;

        &-item {
          margin-bottom: 30rpx;

          &-top {
            display: flex;
            align-items: center;
            justify-content: flex-start;

            &-icon {
              width: 16rpx;
              height: 24rpx;
              background: linear-gradient(180deg, #ffffff 1%, #31dbc3 100%);
              border-radius: 8rpx;
            }

            &-name {
              font-weight: 600;
              font-size: 28rpx;
              color: #333333;
              margin-left: 18rpx;
              flex: 1;
            }

            &-score {
              font-weight: 500;
              font-size: 28rpx;
              color: #f6a52e;
              margin-left: 24rpx;
            }
          }

          &-content {
            margin-top: 24rpx;
            background: #f5f6fa;
            border-radius: 24rpx;
            padding: 30rpx 40rpx;

            &-problem {
              font-weight: 400;
              font-size: 28rpx;
              color: #666666;
              line-height: 34rpx;
              display: block;
            }

            &-recommendations {
              margin-top: 24rpx;
              font-weight: 400;
              font-size: 28rpx;
              color: #29b7a3;
              line-height: 34rpx;
              display: block;
            }
          }
        }
      }
    }
  }
}
</style>
